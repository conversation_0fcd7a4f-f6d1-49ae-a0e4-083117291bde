using Vsp.PayrollConfiguration.CSharp.IntegrationTest.TestCollections;
using Vsp.PayrollConfiguration.Domain.Year.Models;
using Vsp.PayrollConfiguration.Repository.Entities;
using Vsp.PayrollConfiguration.Year.Constants;

namespace Vsp.PayrollConfiguration.CSharp.IntegrationTest.Tests.Year;

[Collection(EntityNames.Year)]
public class PatchYearByWageModelYearIdTests(WebApplicationFactoryFixture<Program, ILoketContext, LoketContext> fixture)
    : CustomIntegrationTestsBase(fixture)
{
    protected override string FolderName => "Year";
    protected override bool UseTransaction => true;

    private static readonly Guid QA_Year_PATCH_WM_2020 = Guid.Parse("00000946-07e4-0000-0000-000000000000");

    [Fact]
    public async Task Ok_NullValues()
    {
        // Act
        var patchUri = $"{YearRoutes.PatchYearByWageModelYearIdAsync}".Replace("{yearId:guid}", QA_Year_PATCH_WM_2020.ToString());
        var patchModel = new YearWmPatchModel()
        {
            StandardShift = null,
            StandardEmployeeProfile = null,
        };
        var patchResult = await CallApiAsync(HttpMethod.Patch, patchUri, patchModel, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(patchResult);

        var loketContext = GetLoketContext();

        var modelYear = await loketContext.Set<ModelYear>().AsNoTracking()
            .Where(GeneratedIdHelper.ConstructWhere<ModelYear>(QA_Year_PATCH_WM_2020))
            .SingleOrDefaultAsync();
        modelYear.Should().NotBeNull();
        modelYear.TestYear.Should().Be(2);
        modelYear.ZwSelfInsurerStartPayrollPeriodNumber.Should().Be(0);
        modelYear.Aof.Should().Be(0);
        modelYear.DateAvailableEss.Should().BeNull();
        modelYear.SendEssMail.Should().Be(2);
    }

    [Theory]
    [InlineData(2, 1)] // Modifying shift value in db
    [InlineData(0, 2)] // Modifying employee profile value in db
    public async Task Ok_NonNullValidValues(int shiftNumber, int employeeProfileNumber)
    {
        // Act
        var patchUri = $"{YearRoutes.PatchYearByWageModelYearIdAsync}".Replace("{yearId:guid}", QA_Year_PATCH_WM_2020.ToString());
        var patchModel = new YearWmPatchModel()
        {
            StandardShift = shiftNumber == 0 ? null : new() { ShiftNumber = shiftNumber },
            StandardEmployeeProfile = new() { EmployeeProfileNumber = employeeProfileNumber },
        };
        var patchResult = await CallApiAsync(HttpMethod.Patch, patchUri, patchModel, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(patchResult);
    }

    [Fact]
    public async Task Ok_EmptyOverride_EntityNotDeleted()
    {
        // Arrange
        var loketContext = GetLoketContext();
        var existingModelEntity = await loketContext.Set<ModelYear>().AsNoTracking()
            .Where(GeneratedIdHelper.ConstructWhere<ModelYear>(QA_Year_PATCH_WM_2020))
            .SingleOrDefaultAsync();
        existingModelEntity.Should().NotBeNull();
        existingModelEntity.StandardShiftNumber.Should().NotBeNull();
        existingModelEntity.StandardEmployeeProfileNumber.Should().NotBeNull();

        // Act
        var patchUri = $"{YearRoutes.PatchYearByWageModelYearIdAsync}".Replace("{yearId:guid}", QA_Year_PATCH_WM_2020.ToString());
        var patchModel = new YearWmPatchModel()
        {
            StandardShift = new() { ShiftNumber = 1 }, // CLA value
            StandardEmployeeProfile = null, // CLA value
        };
        var patchResult = await CallApiAsync(HttpMethod.Patch, patchUri, patchModel, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(patchResult);

        var updatedModelEntity = await loketContext.Set<ModelYear>().AsNoTracking()
            .Where(GeneratedIdHelper.ConstructWhere<ModelYear>(QA_Year_PATCH_WM_2020))
            .SingleOrDefaultAsync();
        updatedModelEntity.Should().NotBeNull();
        updatedModelEntity.StandardShiftNumber.Should().BeNull();
        updatedModelEntity.StandardEmployeeProfileNumber.Should().BeNull();
    }

    [Fact]
    public async Task BadRequest_ModelValidation_Null()
    {
        // Arrange
        YearWmPatchModel? payload = null;

        // Act
        var patchUri = $"{YearRoutes.PatchYearByWageModelYearIdAsync}".Replace("{yearId:guid}", QA_Year_PATCH_WM_2020.ToString());
        var patchResult = await CallApiAsync(HttpMethod.Patch, patchUri, payload, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(patchResult, scrubDates: false);
    }

    [Theory]
    [InlineData(0, 1)] // Out of range for model validation
    [InlineData(16, 1)] // Out of range for model validation
    public async Task BadRequest_ModelValidation_InvalidValuesForShiftNumber(int shiftNumber, int employeeProfileNumber)
    {
        // Act
        var patchUri = $"{YearRoutes.PatchYearByWageModelYearIdAsync}".Replace("{yearId:guid}", QA_Year_PATCH_WM_2020.ToString());
        var patchModel = new YearWmPatchModel()
        {
            StandardShift = new() { ShiftNumber = shiftNumber },
            StandardEmployeeProfile = new() { EmployeeProfileNumber = employeeProfileNumber },
        };
        var patchResult = await CallApiAsync(HttpMethod.Patch, patchUri, patchModel, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(patchResult);
    }

    [Fact]
    public async Task BadRequest_ModelValidation_InvalidValueForEmployeeProfileNumber()
    {
        // Arrange
        var employeeProfileNumber = 0; // Out of range for model validation
        var shiftNumber = 1; // Valid
        // Act
        var patchUri = $"{YearRoutes.PatchYearByWageModelYearIdAsync}".Replace("{yearId:guid}", QA_Year_PATCH_WM_2020.ToString());
        var patchModel = new YearWmPatchModel()
        {
            StandardShift = new() { ShiftNumber = shiftNumber },
            StandardEmployeeProfile = new() { EmployeeProfileNumber = employeeProfileNumber },
        };
        var patchResult = await CallApiAsync(HttpMethod.Patch, patchUri, patchModel, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(patchResult);
    }

    [Fact]
    public async Task BadRequest_ModelValidation_IllegalNullValues()
    {
        // Act
        var patchUri = $"{YearRoutes.PatchYearByWageModelYearIdAsync}".Replace("{yearId:guid}", QA_Year_PATCH_WM_2020.ToString());
        var jsonContent = @"
        {
            standardShift:
            { 
                shiftNumber: null 
            },
            standardEmployeeProfile:
            { 
                employeeProfileNumber: null 
            },
        }";
        var patchResult = await CallApiAsync(HttpMethod.Patch, patchUri, jsonContent, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(patchResult);
    }

    /// <summary>
    /// See: <see cref="MessageCodes.API_PayrollConfiguration_Year_StandardShift_NotFound"/>
    /// </summary>
    [Fact]
    public async Task BadRequest_MessageCode_NonExistingShiftNumber()
    {
        // Arrange
        var shiftNumber = 3; // Non exiting in db
        var employeeProfileNumber = 1; // Valid

        // Act
        var patchUri = $"{YearRoutes.PatchYearByWageModelYearIdAsync}".Replace("{yearId:guid}", QA_Year_PATCH_WM_2020.ToString());
        var patchModel = new YearWmPatchModel()
        {
            StandardShift = new() { ShiftNumber = shiftNumber },
            StandardEmployeeProfile = new() { EmployeeProfileNumber = employeeProfileNumber },
        };
        var patchResult = await CallApiAsync(HttpMethod.Patch, patchUri, patchModel, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(patchResult);
    }

    /// <summary>
    /// See: <see cref="MessageCodes.API_PayrollConfiguration_Year_StandardEmployeeProfile_NotFound"/>
    /// </summary>
    [Fact]
    public async Task BadRequest_MessageCode_NonExistingEmployeeProfileNumber()
    {
        // Arrange
        var shiftNumber = 1; // Valid
        var employeeProfileNumber = 3; // Non exiting in db

        // Act
        var patchUri = $"{YearRoutes.PatchYearByWageModelYearIdAsync}".Replace("{yearId:guid}", QA_Year_PATCH_WM_2020.ToString());
        var patchModel = new YearWmPatchModel()
        {
            StandardShift = new() { ShiftNumber = shiftNumber },
            StandardEmployeeProfile = new() { EmployeeProfileNumber = employeeProfileNumber },
        };
        var patchResult = await CallApiAsync(HttpMethod.Patch, patchUri, patchModel, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(patchResult);
    }
}