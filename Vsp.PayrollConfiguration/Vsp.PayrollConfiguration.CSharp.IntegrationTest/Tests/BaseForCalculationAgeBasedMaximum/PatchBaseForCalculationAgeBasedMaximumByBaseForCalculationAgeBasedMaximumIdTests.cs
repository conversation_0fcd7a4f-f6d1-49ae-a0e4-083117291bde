using Vsp.PayrollConfiguration.CSharp.IntegrationTest.TestCollections;

namespace Vsp.PayrollConfiguration.CSharp.IntegrationTest.Tests.BaseForCalculationAgeBasedMaximum;

[Collection(EntityNames.BaseForCalculationAgeBasedMaximum)]
public class PatchBaseForCalculationAgeBasedMaximumByBaseForCalculationAgeBasedMaximumIdTests(WebApplicationFactoryFixture<Program, ILoketContext, LoketContext> fixture)
    : CustomIntegrationTestsBase(fixture)
{
    protected override string FolderName => "BaseForCalculationAgeBasedMaximum";
    protected override bool UseTransaction => true;
}