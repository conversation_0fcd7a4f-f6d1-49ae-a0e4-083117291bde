using Vsp.PayrollConfiguration.BaseForCalculationBasePayrollComponent.Constants;
using Vsp.PayrollConfiguration.CSharp.IntegrationTest.TestCollections;

namespace Vsp.PayrollConfiguration.CSharp.IntegrationTest.Tests.BaseForCalculationAgeBasedMaximum;

[Collection(EntityNames.BaseForCalculationAgeBasedMaximum)]
public class GetBaseForCalculationAgeBasedMaximumsByBaseForCalculationIdTests(WebApplicationFactoryFixture<Program, ILoketContext, LoketContext> fixture)
    : CustomIntegrationTestsBase(fixture)
{
    protected override string FolderName => "BaseForCalculationAgeBasedMaximum";
    protected override bool UseTransaction => false;
}