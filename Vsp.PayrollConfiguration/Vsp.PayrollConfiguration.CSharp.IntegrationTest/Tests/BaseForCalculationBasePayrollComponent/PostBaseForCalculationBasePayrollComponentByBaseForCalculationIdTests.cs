using Vsp.PayrollConfiguration.CSharp.IntegrationTest.TestCollections;

namespace Vsp.PayrollConfiguration.CSharp.IntegrationTest.Tests.BaseForCalculationBasePayrollComponent;

[Collection(EntityNames.BaseForCalculationBasePayrollComponent)]
public class PostBaseForCalculationBasePayrollComponentByBaseForCalculationIdTests(WebApplicationFactoryFixture<Program, ILoketContext, LoketContext> fixture)
    : CustomIntegrationTestsBase(fixture)
{
    protected override string FolderName => "BaseForCalculationBasePayrollComponent";
    protected override bool UseTransaction => true;
    
    private static readonly Guid QA_BFC_BasePayrollComponent_BFC1_POST_CLA = Guid.Parse("000009f4-07e9-0001-0000-000000000000");
    private static readonly Guid QA_BFC_BasePayrollComponent_BFC1_POST_WM = Guid.Parse("000009f6-07e9-0001-0000-000000000000");
    private static readonly Guid QA_BFC_BasePayrollComponent_BFC1_POST_PA = Guid.Parse("000009fa-07e9-0001-0000-000000000000");

    // [Fact]
    // public async Task Ok_QA_BaseForCalculationBasePayrollComponent_POST_CLA()
    // {
    //  var a = this.GetLoketContext().Set<Repository.Entities.BaseForCalculationBasePayrollComponent>().Where(x => x.InheritanceLevelId == 2548).First();   
    //  GeneratedIdHelper.GenerateId(a);
    // }
    
    
    
}