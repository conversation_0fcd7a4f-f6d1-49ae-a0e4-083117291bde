using Microsoft.EntityFrameworkCore;
using Vsp.PayrollConfiguration.Domain.BaseForCalculationBasePayrollComponent.Extensions;
using Vsp.PayrollConfiguration.Domain.BaseForCalculationBasePayrollComponent.Models;
using Vsp.PayrollConfiguration.Infrastructure.Validators;
using Vsp.PayrollConfiguration.Repository.Interfaces;
using FluentValidation;
using AutoMapper;
using Vsp.PayrollConfiguration.Infrastructure.Constants;

namespace Vsp.PayrollConfiguration.Domain.BaseForCalculationBasePayrollComponent.Validators;

internal class InsertBaseForCalculationBasePayrollComponentValidator
    : AbstractValidator<BaseForCalculationBasePayrollComponentPostModel>
{
    private readonly ILoketContext loketContext;

    public InsertBaseForCalculationBasePayrollComponentValidator(
        ILoketContext loketContext,
        IMapper mapper
    )
    {
        this.loketContext = loketContext;

        Include(new InsertInheritanceEntityValidator<
            BaseForCalculationBasePayrollComponentPostModel,
            Repository.Entities.BaseForCalculationBasePayrollComponent,
            ModelBaseForCalculationBasePayrollComponent>(loketContext, mapper));

        // Basic presence checks first (avoid NREs in async rules)
        RuleFor(m => m.Year).NotNull(); // [Required]

        RuleFor(m => m.PayrollComponent)
            .NotNull()
            .DependentRules(() =>
            {
                RuleFor(m => m.PayrollComponent.Key)
                    .NotNull();
            });

        RuleFor(m => m.StartPayrollPeriod)
            .NotNull()
            .DependentRules(() =>
            {
                RuleFor(m => m.StartPayrollPeriod.PeriodNumber)
                    .NotNull()
                    .GreaterThan(0);
            });

        // ---- Business rule: chosen component must be available for the given base + period ----
        RuleFor(m => m.PayrollComponent.Key)
            .MustAsync(async (model, componentId, ct) =>
            {
                // If period/component not provided, let the presence rules handle it
                if (model.StartPayrollPeriod?.PeriodNumber is not int periodNumber)
                    return true;

                var baseForCalculationId = model.InheritanceLevelId;

                return await loketContext
                    .AvailableBasePayrollComponents(baseForCalculationId, periodNumber)
                    .Where(c => c.ComponentId == componentId)
                    .AnyAsync(ct);
            })
            .WithErrorCode(MessageCodes.API_PayrollConfiguration_BaseForCalculationBasePayrollComponent_PayrollComponent_Invalid)
            .WithMessage("The selected payroll component is not available for the specified base and payroll period.");

        // ---- Optional: ensure at least one component is available for this base + period ----
        RuleFor(m => m.StartPayrollPeriod.PeriodNumber)
            .MustAsync(async (model, periodNumber, ct) =>
            {
                if (periodNumber is null)
                    return true; // presence rule will flag it

                var baseForCalculationId = model.InheritanceLevelId;

                return await loketContext
                    .AvailableBasePayrollComponents(baseForCalculationId, periodNumber.Value)
                    .AnyAsync(ct);
            })
            .WithErrorCode(MessageCodes.API_PayrollConfiguration_BaseForCalculationBasePayrollComponent_PayrollPeriodNumber_Invalid)
            .WithMessage("No available components for the specified base and payroll period.");
    }
}