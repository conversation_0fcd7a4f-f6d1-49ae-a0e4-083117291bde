using Vsp.PayrollConfiguration.Domain.Shared.Interfaces;
using Vsp.PayrollConfiguration.Domain.Shared.Models;
using Vsp.PayrollConfiguration.Domain.Year.Interfaces;
using Vsp.PayrollConfiguration.Domain.Year.Models;
using Vsp.PayrollConfiguration.Infrastructure.Interfaces;
using Vsp.PayrollConfiguration.Repository.Entities.CodeTable;
using Vsp.PayrollConfiguration.Repository.Interfaces;
using InheritanceLevel = Vsp.PayrollConfiguration.Repository.Entities.Base.InheritanceLevel;

namespace Vsp.PayrollConfiguration.Domain.Year.Services;

internal class YearService(
    IFilteredQuery<YearMinimizedModel, Repository.Entities.Year, InheritanceLevel, ILoketContext> getYearsMinimizedQuery,
    IFilteredQuery<YearClaWmModel, Repository.Entities.Year, InheritanceLevel, ILoketContext> getYearsClaWmQuery,
    IFilteredQuery<YearPaModel, Repository.Entities.Year, InheritanceLevel, ILoketContext> getYearsPaQuery,
    IFilteredQuery<PayrollPeriodModel, PayrollPeriodPayrollTaxReturn, Repository.Entities.Year, ILoketContext> getPayrollPeriodsQuery,
    IPatchInheritanceEntityCommand<YearWmPatchModel, YearClaWmModel, Repository.Entities.Year, ModelYear> patchWmCommand,
    IPatchInheritanceEntityCommand<YearPaPatchModel, YearPaModel, Repository.Entities.Year, ModelYear> patchPaCommand,
    ILoketContext dbContext,
    IMapper mapper,
    IYearMetadataHelper metadataHelper,
    ICodeTableHelper codeTableHelper
) : IYearService
{
    private readonly IFilteredQuery<YearMinimizedModel, Repository.Entities.Year, InheritanceLevel, ILoketContext> getYearsMinimizedQuery = getYearsMinimizedQuery;
    private readonly IFilteredQuery<YearClaWmModel, Repository.Entities.Year, InheritanceLevel, ILoketContext> getYearsClaWmQuery = getYearsClaWmQuery;
    private readonly IFilteredQuery<YearPaModel, Repository.Entities.Year, InheritanceLevel, ILoketContext> getYearsPaQuery = getYearsPaQuery;
    private readonly IFilteredQuery<PayrollPeriodModel, PayrollPeriodPayrollTaxReturn, Repository.Entities.Year, ILoketContext> getPayrollPeriodsQuery = getPayrollPeriodsQuery;
    private readonly IPatchInheritanceEntityCommand<YearWmPatchModel, YearClaWmModel, Repository.Entities.Year, ModelYear> patchWmCommand = patchWmCommand;
    private readonly IPatchInheritanceEntityCommand<YearPaPatchModel, YearPaModel, Repository.Entities.Year, ModelYear> patchPaCommand = patchPaCommand;
    private readonly ILoketContext dbContext = dbContext;
    private readonly IMapper mapper = mapper;
    private readonly IYearMetadataHelper metadataHelper = metadataHelper;
    private readonly ICodeTableHelper codeTableHelper = codeTableHelper;

    public async Task<IListOperationResult<YearMinimizedModel>> GetYearsMinimizedByInheritanceLevelIdAsync(Guid inheritanceLevelId) =>
        await this.getYearsMinimizedQuery.ExecuteList(inheritanceLevelId);

    public async Task<IListOperationResult<YearClaWmModel>> GetYearsByCollectiveLaborAgreementOrWageModelIdAsync(Guid collectiveLaborAgreementOrWageModelId)
    {
        var result = await this.getYearsClaWmQuery.ExecuteList(collectiveLaborAgreementOrWageModelId);

        if (!result.Success)
        {
            return result;
        }

        var yearListModel = result.ResultObject.ToList();

        // TODO Improve/change this.
        await EnrichYearModelsWithStandardShiftAsync(yearListModel);

        return result;
    }

    public async Task<IListOperationResult<YearPaModel>> GetYearsByPayrollAdministrationIdAsync(Guid payrollAdministrationId)
    {
        var result = await this.getYearsPaQuery.ExecuteList(payrollAdministrationId);

        if (!result.Success)
        {
            return result;
        }

        var yearListModel = result.ResultObject.ToList();
        await EnrichYearModelsWithStandardShiftAsync(yearListModel);

        return result;
    }

    public async Task<IOperationResult<YearClaWmModel>> PatchYearByWageModelYearIdAsync(Guid yearId, YearWmPatchModel patchModel)
    {
        patchModel.Id = yearId;
        var patchResult = await this.patchWmCommand.ExecuteAsync(patchModel);
        if (patchResult.Success)
        {
            await EnrichYearModelWithStandardShiftAsync(patchResult.ResultObject);
        }

        return patchResult;
    }

    public async Task<IOperationResult<YearPaModel>> PatchYearByPayrollAdministrationYearIdAsync(Guid yearId, YearPaPatchModel patchModel)
    {
        patchModel.Id = yearId;
        var patchResult = await this.patchPaCommand.ExecuteAsync(patchModel);
        if (patchResult.Success)
        {
            await EnrichYearModelWithStandardShiftAsync(patchResult.ResultObject);
        }

        return patchResult;
    }

    public async Task<IOperationResult<TModel>> GetYearMetadataByYearIdAsync<TModel>(Guid yearId) where TModel : YearMetadataModel, new()
    {
        var shifts = this.metadataHelper.GetStandardShiftsFuture(yearId);
        var employeeProfiles = this.metadataHelper.GetStandardEmployeeProfilesFuture(yearId);

        var model = new TModel();

        if (model is YearMetadataPaModel yearMetadataPaModel)
        {
            yearMetadataPaModel.Aof = await this.codeTableHelper.GetOptions<CtAof>();
        }

        model.StandardShift = await shifts.ToListAsync();
        model.StandardEmployeeProfile = await employeeProfiles.ToListAsync();

        return new OperationResult<TModel>(model);
    }

    public async Task<IListOperationResult<PayrollPeriodModel>> GetPayrollPeriodsByYearIdAsync(Guid yearId) =>
        await this.getPayrollPeriodsQuery.ExecuteList(yearId);

    private async Task EnrichYearModelWithStandardShiftAsync<T>(T yearModel) where T : YearClaWmModel =>
        await EnrichYearModelsWithStandardShiftAsync(new List<YearClaWmModel>() { yearModel });

    private async Task EnrichYearModelsWithStandardShiftAsync<T>(List<T> yearListModel) where T : YearClaWmModel
    {
        // Sanity check
        if (yearListModel.Count == 0) return;

        // Create separate sets for needed shifts -- PK is (InheritanceLevelId, YearId, ShiftId)
        var inheritanceLevelIds = yearListModel.Select(y => y.InheritanceLevelId).ToHashSet();
        var yearIds = yearListModel.Select(y => y.Year).ToHashSet();
        var shiftIds = yearListModel.Select(y => y.StandardShiftNumber).ToHashSet();

        // Get all shifts in a single query with minimum PayrollPeriodId for each group
        var shifts = await this.dbContext.Set<Repository.Entities.Shift>()
            .Where(s => inheritanceLevelIds.Contains(s.InheritanceLevelId) && yearIds.Contains(s.YearId) && shiftIds.Contains(s.ShiftId))
            .GroupBy(s => new { s.InheritanceLevelId, s.YearId, s.ShiftId })
            .ToDictionaryAsync(
                g => (g.Key.InheritanceLevelId, g.Key.YearId, g.Key.ShiftId),
                g => g.MinBy(s => s.PayrollPeriodId)
            );

        // Apply shifts to models
        foreach (var yearModel in yearListModel)
        {
            var shiftKey = (yearModel.InheritanceLevelId, yearModel.Year, yearModel.StandardShiftNumber);

            var shift = shifts.GetValueOrDefault(shiftKey) ?? new Repository.Entities.Shift
            {
                InheritanceLevelId = yearModel.InheritanceLevelId,
                YearId = yearModel.Year,
                ShiftId = yearModel.StandardShiftNumber,
                BonusPercentage = 0,
                FullTimeHoursPerWeek = 0,
            };

            yearModel.StandardShift = shift.ShiftId == 0 ? null : this.mapper.Map<StandardShiftModel>(shift);
        }
    }
}